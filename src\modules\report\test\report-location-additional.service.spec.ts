import { BadRequestException } from '@nestjs/common';
import { Test } from '@nestjs/testing';

import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockContact } from '~/test/mocks/contact.mock';
import { initMockLocation } from '~/test/mocks/location.mock';
import { initMockLocationAdditional } from '~/test/mocks/locationadditional.mock';
import { initMockLocationAdditionalGroupName } from '~/test/mocks/locationadditionalgroupname.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import { initMockTenant } from '~/test/mocks/tenant.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';

import { ReportLocationAdditionalService } from '../location-additional/report-location-additional.service';
import { reportLocationTest } from './report-location-additional.dto.test';

describe('ReportLocationAdditionalService', () => {
  let service: ReportLocationAdditionalService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ReportLocationAdditionalService,
        ...testInjectProviders([
          TeamModel,
          LocationAdditionalModel,
          TenantUserModel,
          TenantModel,
        ]),
      ],
    }).compile();

    service = module.get(ReportLocationAdditionalService);

    await Promise.all([
      initMockTeam(),
      initMockTenant(),
      initMockTenantUser(),
      initMockContact(),
      initMockLocation(),
      initMockLocationAdditional(),
      initMockLocationAdditionalGroupName(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getAdditionalLocations', () => {
    it('should throw error if team or category is not provided', async () => {
      await expect(
        service.getAdditionalLocations({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        }),
      ).rejects.toThrow(BadRequestException);

      await expect(
        service.getAdditionalLocationsNew({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should call fn and return list data', async () => {
      const result = await service.getAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        reportLocationTest.getAdditionalLocationSchema,
      );

      await expect(
        service.getAdditionalLocationsNew({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
          team: mockTeamData._id.toString(),
        }),
      ).resolves.toMatchSchema(reportLocationTest.getAdditionalLocationSchema);
    });
  });

  describe('exportAdditionalLocations', () => {
    it('should call fn and return data for export', async () => {
      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
    });

    it('should return correct header and filename for FEATURE_AND_SUPPLIER type', async () => {
      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');

      // Test specific header structure for FEATURE_AND_SUPPLIER
      expect(result.header).toEqual([
        { field: 'team', title: 'Team' },
        { field: 'location', title: 'Location' },
        { field: 'category', title: 'Item Category' },
        { field: 'responsible', title: 'Responsible' },
        { field: 'supplier', title: 'Supplier' },
        { field: 'code', title: 'Code' },
      ]);

      // Test filename format
      expect(result.fileName).toMatch(
        /^feature-and-supplier-report-\d{4}-\d{2}-\d{2}\.csv$/,
      );
    });

    it('should test parseYearInstallation helper function with different input types', async () => {
      // Create mock data with different yearInstallation values
      await initMockLocationAdditional({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        yearInstallation: 2020, // number
      });

      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstItem = result.data[0];
        expect(firstItem).toHaveProperty('yearInstallation');
        // Should handle number input correctly
        expect(
          typeof firstItem.yearInstallation === 'number' ||
            firstItem.yearInstallation === '',
        ).toBe(true);
      }
    });

    it('should test parseYearInstallation with date string input', async () => {
      // Create mock data with date string yearInstallation
      await initMockLocationAdditional({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        yearInstallation: new Date('2021-06-15').getTime(), // date as timestamp
      });

      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstItem = result.data[0];
        expect(firstItem).toHaveProperty('yearInstallation');
        // Should extract year from valid date
        expect(
          firstItem.yearInstallation === 2021 ||
            firstItem.yearInstallation === '',
        ).toBe(true);
      }
    });

    it('should test formatDateCheck helper function', async () => {
      // Create mock data with dateCheck value
      const testDate = new Date('2023-05-15');
      await initMockLocationAdditional({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        dateCheck: testDate,
      });

      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstItem = result.data[0];
        expect(firstItem).toHaveProperty('dateCheck');
        // Should format date correctly or be empty string
        expect(typeof firstItem.dateCheck).toBe('string');
        if (firstItem.dateCheck) {
          expect(firstItem.dateCheck).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        }
      }
    });

    it('should handle null/undefined values in parseYearInstallation and formatDateCheck', async () => {
      // Create mock data with undefined values
      await initMockLocationAdditional({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        yearInstallation: undefined,
        dateCheck: undefined,
      });

      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstItem = result.data[0];
        // Should handle undefined values gracefully
        expect(firstItem.yearInstallation).toBe('');
        expect(firstItem.dateCheck).toBe('');
      }
    });
  });
});
