import { BadRequestException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockContact } from '~/test/mocks/contact.mock';
import { initMockLocation } from '~/test/mocks/location.mock';
import { initMockLocationAdditional } from '~/test/mocks/locationadditional.mock';
import { initMockLocationAdditionalGroupName } from '~/test/mocks/locationadditionalgroupname.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import { initMockTenant } from '~/test/mocks/tenant.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';

import { ReportLocationAdditionalService } from '../location-additional/report-location-additional.service';
import { reportLocationTest } from './report-location-additional.dto.test';

describe('ReportLocationAdditionalService', () => {
  let service: ReportLocationAdditionalService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ReportLocationAdditionalService,
        ...testInjectProviders([
          TeamModel,
          LocationAdditionalModel,
          TenantUserModel,
          TenantModel,
        ]),
      ],
    }).compile();

    service = module.get(ReportLocationAdditionalService);

    await Promise.all([
      initMockTeam(),
      initMockTenant(),
      initMockTenantUser(),
      initMockContact(),
      initMockLocation(),
      initMockLocationAdditional(),
      initMockLocationAdditionalGroupName(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getAdditionalLocations', () => {
    it('should throw error if team or category is not provided', async () => {
      await expect(
        service.getAdditionalLocations({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        }),
      ).rejects.toThrow(BadRequestException);

      await expect(
        service.getAdditionalLocationsNew({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should call fn and return list data', async () => {
      const result = await service.getAdditionalLocations({
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        team: mockTeamData._id.toString(),
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        reportLocationTest.getAdditionalLocationSchema,
      );

      await expect(
        service.getAdditionalLocationsNew({
          type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
          team: mockTeamData._id.toString(),
        }),
      ).resolves.toMatchSchema(reportLocationTest.getAdditionalLocationSchema);
    });
  });

  describe('exportAdditionalLocations', () => {
    // it('should export location type certificate_and_control', async () => {
    //   const result = await service.exportAdditionalLocations({
    //     type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
    //     team: mockTeamData._id.toString(),
    //     user: mockTenantUserData._id.toString(),
    //   });
    //   expect(result).toHaveProperty('data');
    //   expect(result).toHaveProperty('header');
    //   expect(result).toHaveProperty('fileName');
    // });

    it('should export location type feature_and_supplier', async () => {
      await Promise.all([
        initMockLocationAdditional({
          type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        }),
        initMockLocationAdditional({
          _id: new ObjectId(),
          type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
          yearInstallation: '' as any,
        }),
        initMockLocationAdditional({
          _id: new ObjectId(),
          type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
          yearInstallation: new Date().toISOString() as any,
        }),
      ]);

      const result = await service.exportAdditionalLocations({
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        team: mockTeamData._id.toString(),
        user: mockTenantUserData._id.toString(),
      });

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
    });
  });
});
