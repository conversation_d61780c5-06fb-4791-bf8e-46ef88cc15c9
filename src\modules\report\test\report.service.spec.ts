import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { DEPOSIT_COST_TYPE_ITEM_CODE } from '~/constants/app.constant';
import { AVG_WEEKS_IN_MONTH } from '~/modules/contract/contract.helper';
import { ContractModel } from '~/modules/contract/contract.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { JobModel } from '~/modules/job/job.model';
import {
  DebtorAndCreditorReportQueryDto,
  DebtorServiceRevenueHiredLocationQueryDto,
  ReportEmployeeQueryParamsDto,
  RevenueHiredLocationQueryDto,
} from '~/modules/report/dtos/report.dto';
import { ReportService } from '~/modules/report/report.service';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
  ContractType,
} from '~/shared/enums/contract.enum';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockAddress } from '~/test/mocks/address.mock';
import { initMockAgreementLine } from '~/test/mocks/agreementline.mock';
import {
  initMockBvCompany,
  mockBvCompanyData,
} from '~/test/mocks/bvcompany.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import {
  initMockCostCenter,
  mockCostCenterData,
} from '~/test/mocks/costcenter.mock';
import { initMockCostline } from '~/test/mocks/costline.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockCostType, mockCostTypeData } from '~/test/mocks/costtype.mock';
import { initMockCountry } from '~/test/mocks/country.mock';
import { initMockJob } from '~/test/mocks/job.mock';
import { initMockJobEmployee } from '~/test/mocks/jobemployee.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockRegion } from '~/test/mocks/region.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';

import { reportTest } from './report.dto.test';

async function initMockContractAndRelatedData(
  contractId: ObjectId,
  type: ContractType = ContractType.RENTING,
  agreementLinePeriod: AgreementLinePeriod = AgreementLinePeriod.WEEKLY,
  weeklyPrice: number = 100.0,
  isNew: boolean = false,
  depositPrice: number = 0,
) {
  const agreementLineId = new ObjectId();
  const costLineGeneralId = new ObjectId();
  const depositAgreementLineId = new ObjectId();
  const depositCostLineGeneralId = new ObjectId();
  const depositCostTypeId = new ObjectId();

  const agreementLines = [agreementLineId];

  // Add separate deposit agreement line if depositPrice > 0
  if (depositPrice > 0) {
    agreementLines.push(depositAgreementLineId);
  }

  await Promise.all([
    initMockContract({
      _id: contractId,
      endDate: dayjs().add(69, 'days').toDate(),
      agreementLines,
      type,
      isNew,
    }),
    // Main accommodation agreement line
    initMockAgreementLine({
      _id: agreementLineId,
      contract: contractId,
      type: AgreementLineType.ACCOMMODATION,
      period: agreementLinePeriod,
      costLineGenerals: [costLineGeneralId],
    }),
    initMockCostlineGeneral({
      _id: costLineGeneralId,
      agreementLine: agreementLineId,
      price: weeklyPrice,
      endDate: dayjs().add(30, 'days').toDate(),
    }),
    // Add separate deposit agreement line and cost type if needed
    ...(depositPrice > 0
      ? [
          initMockCostType({
            _id: depositCostTypeId,
            itemCode: DEPOSIT_COST_TYPE_ITEM_CODE,
            name: 'Borg',
            description: 'Deposit',
          }),
          initMockAgreementLine({
            _id: depositAgreementLineId,
            contract: contractId,
            type: AgreementLineType.PRODUCT, // Use PRODUCT for deposit
            periodType: AgreementLinePeriodType.ONE_TIME,
            costLineGenerals: [depositCostLineGeneralId],
          }),
          initMockCostlineGeneral({
            _id: depositCostLineGeneralId,
            agreementLine: depositAgreementLineId,
            price: depositPrice,
            costType: depositCostTypeId,
            endDate: dayjs().add(30, 'days').toDate(),
          }),
        ]
      : []),
  ]);
}

const findContract = (contracts: any[], contractId: ObjectId) => {
  return contracts.find(
    (contract) => contract._id.toString() === contractId.toString(),
  );
};

// convert these code to helper function
const findContractsInReport = (reports: any[], contractId: ObjectId) => {
  for (const report of reports) {
    // Check in debtor contracts
    const debtorContract = findContract(
      report?.debtorRentingContracts ?? [],
      contractId,
    );
    if (debtorContract) {
      return debtorContract;
    }

    // Check in creditor contracts
    const creditorContract = findContract(
      report?.creditorRentingContracts ?? [],
      contractId,
    );
    if (creditorContract) {
      return creditorContract;
    }
  }
  return undefined;
};

describe('ReportService', () => {
  let service: ReportService;

  const debtorContractId = mockContractData._id;
  const creditorContractId = new ObjectId();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ReportService,
        ...testInjectModel([
          ContractModel,
          TenantUserModel,
          JobModel,
          CostTypeModel,
        ]),
      ],
    }).compile();

    await Promise.all([
      initMockLocation(),
      initMockAddress(),
      initMockCountry(),
      initMockRegion(),
      initMockContact(),
      initMockCostCenter(),
      initMockBvCompany(),
      initMockCostType(),
      initMockContractAndRelatedData(
        debtorContractId,
        ContractType.RENTING,
        AgreementLinePeriod.WEEKLY,
        100.0,
        false,
        150.0, // deposit
      ),
      initMockContractAndRelatedData(
        creditorContractId,
        ContractType.CREDITOR,
        AgreementLinePeriod.MONTHLY,
        200.0,
        false,
        300.0, // deposit
      ),
      initMockCostline({
        periodType: AgreementLinePeriodType.PERIODIC,
        approvedAt: new Date(),
        startDate: dayjs().startOf('month').toDate(),
        endDate: dayjs().endOf('month').toDate(),
      }),
      initMockContract({
        type: ContractType.RENTING,
        startDate: dayjs().startOf('month').toDate(),
        endDate: dayjs().endOf('month').toDate(),
      }),
      initMockAgreementLine({
        contract: mockContractData._id,
        costLineGenerals: [mockCostlineGeneralData._id],
      }),
      initMockCostlineGeneral({
        endDate: dayjs().add(30, 'days').toDate(),
      }),
      initMockJobEmployee(),
      initMockTeam(),
      initMockJob(),
      initMockTenantUser(),
    ]);

    service = module.get(ReportService);
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getRevenueHiredLocations', () => {
    it('should return revenue for hired locations', async () => {
      const payload: RevenueHiredLocationQueryDto = {
        contractType: ContractType.RENTING,
        month: dayjs().month().toString(),
        year: dayjs().year().toString(),
        costType: mockCostTypeData._id.toString(),
        contact: mockContactData._id.toString(),
        location: mockLocationData._id.toString(),
        bvCompany: mockBvCompanyData._id.toString(),
        sortBy: 'updatedAt',
        sortDir: 'asc',
      };

      const result = await service.getRevenueHiredLocations(payload);
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('totalPrice');
      expect(result).toMatchSchema(reportTest.getRevenueHiredLocationsSchema);
    });

    it('should return empty array if no data found', async () => {
      const payload: DebtorServiceRevenueHiredLocationQueryDto = {
        contractType: ContractType.SERVICE,
        year: dayjs().add(1, 'year').year().toString(),
        costType: mockCostTypeData._id.toString(),
        contact: mockContactData._id.toString(),
        costCenter: [mockCostCenterData._id.toString()],
        sortBy: 'updatedAt',
        sortDir: 'asc',
      };

      const result = await service.getRevenueHiredLocations(payload);
      expect(result).toEqual({ items: [], totalPrice: 0 });
    });
  });

  describe('exportRevenueHiredLocations', () => {
    it('should export revenue for hired locations', async () => {
      const payload: RevenueHiredLocationQueryDto = {
        contractType: ContractType.RENTING,
        month: dayjs().month().toString(),
        year: dayjs().year().toString(),
        costType: mockCostTypeData._id.toString(),
        contact: mockContactData._id.toString(),
        location: mockLocationData._id.toString(),
        bvCompany: mockBvCompanyData._id.toString(),
        sortBy: 'updatedAt',
        sortDir: 'asc',
      };

      const result = await service.exportRevenueHiredLocations(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
    });

    it('should export with correct CSV data structure for renting contracts', async () => {
      const payload: RevenueHiredLocationQueryDto = {
        contractType: ContractType.RENTING,
        month: '1',
        year: dayjs().year().toString(),
        costType: mockCostTypeData._id.toString(),
        contact: mockContactData._id.toString(),
        location: mockLocationData._id.toString(),
        bvCompany: mockBvCompanyData._id.toString(),
        sortBy: 'updatedAt',
        sortDir: 'asc',
      };

      const result = await service.exportRevenueHiredLocations(payload);
      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstRow = result.data[0];
        expect(firstRow).toHaveProperty('month');
        expect(firstRow).toHaveProperty('costCenter');
        expect(firstRow).toHaveProperty('itemCode');
        expect(firstRow).toHaveProperty('costType');
        expect(firstRow).toHaveProperty('location'); // Should have location for renting
        expect(firstRow).toHaveProperty('bvCompany');
        expect(firstRow).toHaveProperty('contact');
        expect(firstRow).toHaveProperty('totalPrice');
        expect(firstRow.month).toBe('01-' + dayjs().year().toString());
      }
    });

    it('should export with correct CSV data structure for service contracts', async () => {
      const payload: DebtorServiceRevenueHiredLocationQueryDto = {
        contractType: ContractType.SERVICE,
        year: dayjs().year().toString(),
        costType: mockCostTypeData._id.toString(),
        contact: mockContactData._id.toString(),
        costCenter: [mockCostCenterData._id.toString()],
        bvCompany: mockBvCompanyData._id.toString(),
        sortBy: 'updatedAt',
        sortDir: 'asc',
      };

      const result = await service.exportRevenueHiredLocations(payload);
      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);

      if (result.data.length > 0) {
        const firstRow = result.data[0];
        expect(firstRow).toHaveProperty('month');
        expect(firstRow).toHaveProperty('costCenter');
        expect(firstRow).toHaveProperty('itemCode');
        expect(firstRow).toHaveProperty('costType');
        expect(firstRow).not.toHaveProperty('location'); // Should NOT have location for service
        expect(firstRow).toHaveProperty('bvCompany');
        expect(firstRow).toHaveProperty('contact');
        expect(firstRow).toHaveProperty('totalPrice');
        expect(firstRow.month).toBe(dayjs().year().toString());
      }
    });
  });

  const payload: DebtorAndCreditorReportQueryDto = {
    week: dayjs().utc().isoWeek().toString(),
    year: dayjs().utc().year().toString(),
  };
  let debtorAndCreditorReports: any[];

  describe('getDebtorAndRentingReport', () => {
    it('should have both debtor and creditor reports', async () => {
      debtorAndCreditorReports =
        await service.getDebtorAndRentingReport(payload);

      expect(debtorAndCreditorReports).toBeDefined();
      expect(debtorAndCreditorReports.length).toBeGreaterThan(0);
      expect(
        debtorAndCreditorReports[0].debtorRentingContracts.length,
      ).toBeGreaterThan(0);
      expect(
        debtorAndCreditorReports[0].creditorRentingContracts.length,
      ).toBeGreaterThan(0);
    });

    it('should filter by customer contact when provided', async () => {
      const payloadWithCustomer: DebtorAndCreditorReportQueryDto = {
        ...payload,
        customer: mockContactData._id.toString(),
      };

      const result =
        await service.getDebtorAndRentingReport(payloadWithCustomer);
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // Should return results filtered by customer contact
    });

    it('should filter by creditor contact when provided', async () => {
      const payloadWithCreditor: DebtorAndCreditorReportQueryDto = {
        ...payload,
        creditor: mockContactData._id.toString(),
      };

      const result =
        await service.getDebtorAndRentingReport(payloadWithCreditor);
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // Should return results filtered by creditor contact
    });

    it('should filter by both customer and creditor when provided', async () => {
      const payloadWithBothContacts: DebtorAndCreditorReportQueryDto = {
        ...payload,
        customer: mockContactData._id.toString(),
        creditor: mockContactData._id.toString(),
      };

      const result = await service.getDebtorAndRentingReport(
        payloadWithBothContacts,
      );
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // Should return results filtered by both contacts
    });

    it('should filter by bvCompany when provided', async () => {
      const payloadWithBvCompany: DebtorAndCreditorReportQueryDto = {
        ...payload,
        bvCompany: mockBvCompanyData._id.toString(),
      };

      const result =
        await service.getDebtorAndRentingReport(payloadWithBvCompany);
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // Should return results filtered by bvCompany
    });

    it('should filter by team when provided', async () => {
      const payloadWithTeam: DebtorAndCreditorReportQueryDto = {
        ...payload,
        team: mockTeamData._id.toString(),
      };

      const result = await service.getDebtorAndRentingReport(payloadWithTeam);
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // Should return results filtered by team
    });

    it('debtor report should use old formula to calculate monthlyPrice for WEEKLY agreement lines when isNew is false', async () => {
      await initMockContractAndRelatedData(
        debtorContractId,
        ContractType.RENTING,
        AgreementLinePeriod.WEEKLY,
        100.0,
        false, // isNew = false for old formula
        150.0, // deposit
      );

      debtorAndCreditorReports =
        await service.getDebtorAndRentingReport(payload);

      const debtorContract = findContractsInReport(
        debtorAndCreditorReports,
        debtorContractId,
      );

      expect(debtorContract).toBeDefined();
      expect(debtorContract.monthlyPrice).toEqual(100.0 * AVG_WEEKS_IN_MONTH);
    });

    it('creditor report should use old formula to calculate monthlyPrice for MONTHLY agreement lines when isNew is false', async () => {
      await initMockContractAndRelatedData(
        creditorContractId,
        ContractType.CREDITOR,
        AgreementLinePeriod.MONTHLY,
        200.0,
        false, // isNew = false for old formula
        300.0, // deposit
      );

      const testReports = await service.getDebtorAndRentingReport(payload);

      const creditorContract = findContractsInReport(
        testReports,
        creditorContractId,
      );

      expect(creditorContract).toBeDefined();
      // For MONTHLY period with old formula: price * 4.333333
      expect(creditorContract.monthlyPrice).toEqual(200.0 * 4.333333);
    });

    it('debtor report should use new formula to calculate monthlyPrice for WEEKLY agreement lines when isNew is true', async () => {
      const newContractId = new ObjectId();
      await initMockContractAndRelatedData(
        newContractId,
        ContractType.RENTING,
        AgreementLinePeriod.WEEKLY,
        100.0,
        true, // isNew = true for new formula
        0, // no deposit to keep calculation simple
      );

      const testReports = await service.getDebtorAndRentingReport(payload);

      const newContract = findContractsInReport(testReports, newContractId);

      expect(newContract).toBeDefined();
      // For WEEKLY period with new formula: (price / 7) * (365 / 12)
      expect(newContract.monthlyPrice).toEqual((100.0 / 7) * (365 / 12));
    });

    it('should use new formula for MONTHLY agreement lines when isNew is true', async () => {
      const newMonthlyContractId = new ObjectId();
      await initMockContractAndRelatedData(
        newMonthlyContractId,
        ContractType.RENTING,
        AgreementLinePeriod.MONTHLY,
        150.0,
        true, // isNew = true for new formula
        0, // no deposit
      );

      const testReports = await service.getDebtorAndRentingReport(payload);

      const newContract = findContractsInReport(
        testReports,
        newMonthlyContractId,
      );

      expect(newContract).toBeDefined();
      // For MONTHLY period with new formula: (price / 7) * (365 / 12)
      expect(newContract.monthlyPrice).toEqual((150.0 / 7) * (365 / 12));
    });

    it('debtor report should include deposit field calculated correctly', async () => {
      const testReports = await service.getDebtorAndRentingReport(payload);

      const debtorContract = findContractsInReport(
        testReports,
        debtorContractId,
      );

      expect(debtorContract).toBeDefined();
      expect(debtorContract.deposit).toBeDefined();
      expect(debtorContract.deposit).toEqual(150.0);
    });

    it('creditor report should include deposit field calculated correctly', async () => {
      const testReports = await service.getDebtorAndRentingReport(payload);

      const creditorContract = testReports
        .flatMap((report) => report.creditorRentingContracts)
        .find(
          (contract) =>
            contract._id.toString() === creditorContractId.toString(),
        );

      expect(creditorContract).toBeDefined();
      expect(creditorContract.deposit).toBeDefined();
      expect(creditorContract.deposit).toEqual(300.0);
    });

    it('should calculate deposit correctly from cost lines with itemCode 1350N', async () => {
      const testContractId = new ObjectId();
      await initMockContractAndRelatedData(
        testContractId,
        ContractType.RENTING,
        AgreementLinePeriod.WEEKLY,
        50.0,
        false,
        250.0, // deposit with itemCode 1350N
      );

      const testReports = await service.getDebtorAndRentingReport(payload);

      const contractWithDeposit = findContractsInReport(
        testReports,
        testContractId,
      );

      expect(contractWithDeposit).toBeDefined();
      expect(contractWithDeposit.deposit).toEqual(250.0);
    });

    it('should return zero deposit when no cost lines with itemCode 1350N exist', async () => {
      const testContractId = new ObjectId();
      await initMockContractAndRelatedData(
        testContractId,
        ContractType.RENTING,
        AgreementLinePeriod.WEEKLY,
        75.0,
        false,
        0, // no deposit
      );

      const testReports = await service.getDebtorAndRentingReport(payload);

      const contractWithoutDeposit = findContractsInReport(
        testReports,
        testContractId,
      );

      expect(contractWithoutDeposit).toBeDefined();
      expect(contractWithoutDeposit.deposit).toEqual(0);
    });

    it('should include owner field in debtor contracts', async () => {
      const testReports = await service.getDebtorAndRentingReport(payload);

      const debtorContract = findContractsInReport(
        testReports,
        debtorContractId,
      );

      expect(debtorContract).toBeDefined();
      expect(debtorContract.owner).toBeDefined();
      expect(debtorContract.owner).toMatch(/^(HOMEE|LENTO)$/i);
    });

    it('should include pricePerBed and otherCostsPerBed fields', async () => {
      const testReports = await service.getDebtorAndRentingReport(payload);

      const debtorContract = findContractsInReport(
        testReports,
        debtorContractId,
      );

      expect(debtorContract).toBeDefined();
      expect(debtorContract.pricePerBed).toBeDefined();
      expect(debtorContract.otherCostsPerBed).toBeDefined();
      expect(typeof debtorContract.pricePerBed).toBe('number');
      expect(typeof debtorContract.otherCostsPerBed).toBe('number');
    });

    it('should return reports with correct structure including location, costCenter, bvCompany, and team fields', async () => {
      const testReports = await service.getDebtorAndRentingReport(payload);

      expect(testReports).toBeDefined();
      expect(Array.isArray(testReports)).toBe(true);
      expect(testReports.length).toBeGreaterThan(0);

      const firstReport = testReports[0];
      expect(firstReport.location).toBeDefined();
      expect(firstReport.location._id).toBeDefined();
      expect(firstReport.location.fullAddress).toBeDefined();
      expect(firstReport.costCenter).toBeDefined();
      expect(firstReport.bvCompany).toBeDefined();
      expect(firstReport.team).toBeDefined();
      expect(Array.isArray(firstReport.debtorRentingContracts)).toBe(true);
      expect(Array.isArray(firstReport.creditorRentingContracts)).toBe(true);
    });
  });

  describe('exportDebtorAndRentingReport', () => {
    it('should export debtor and creditor reports', async () => {
      const result = await service.exportDebtorAndRentingReport(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
    });
  });

  describe('getListEmployees', () => {
    it('should return list of employees', async () => {
      const payload: ReportEmployeeQueryParamsDto = {
        month: dayjs().month() + 1,
        year: dayjs().year(),
      };

      const result = await service.getListEmployees(payload);
      expect(result).toMatchSchema(reportTest.getListEmployeesSchema);
    });
  });

  describe('exportListEmployees', () => {
    it('should export list of employees', async () => {
      const payload: ReportEmployeeQueryParamsDto = {
        month: dayjs().month() + 1,
        year: dayjs().year(),
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.exportListEmployees(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
    });
  });
});
